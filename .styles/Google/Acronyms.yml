extends: conditional
message: "Spell out '%s', if it's unfamiliar to the audience."
link: 'https://developers.google.com/style/abbreviations'
level: suggestion
ignorecase: false
# Ensures that the existence of 'first' implies the existence of 'second'.
first: '\b([A-Z]{3,5})\b'
second: '(?:\b[A-Z][a-z]+ )+\(([A-Z]{3,5})\)'
# ... with the exception of these:
exceptions:
  - API
  - ASP
  - CLI
  - CPU
  - CSS
  - CSV
  - DEBUG
  - DOM
  - DPI
  - FAQ
  - GCC
  - GDB
  - GET
  - GPU
  - GTK
  - GUI
  - HTML
  - HTTP
  - HTTPS
  - IDE
  - JAR
  - JSON
  - JSX
  - LESS
  - LLDB
  - NET
  - NOTE
  - NVDA
  - OSS
  - PATH
  - PDF
  - PHP
  - POST
  - RAM
  - REPL
  - RSA
  - SCM
  - SCSS
  - SDK
  - SQL
  - SSH
  - SSL
  - SVG
  - TBD
  - TCP
  - TODO
  - URI
  - URL
  - USB
  - UTF
  - XML
  - XSS
  - YAML
  - ZIP
