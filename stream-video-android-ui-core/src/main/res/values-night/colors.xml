<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (c) 2024 Stream.io Inc. All rights reserved.

     Licensed under the Stream License;
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          https://github.com/GetStream/stream-video-android/blob/main/LICENSE

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>
    <color name="stream_video_text_high_emphasis">@color/stream_video_text_high_emphasis_dark</color>
    <color name="stream_video_text_low_emphasis">@color/stream_video_text_low_emphasis_dark</color>
    <color name="stream_video_disabled">@color/stream_video_disabled_dark</color>
    <color name="stream_video_borders">@color/stream_video_borders_dark</color>
    <color name="stream_video_input_background">@color/stream_video_input_background_dark</color>
    <color name="stream_video_app_background">@color/stream_video_app_background_dark</color>
    <color name="stream_video_bars_background">@color/stream_video_bars_background_dark</color>
    <color name="stream_video_link_background">@color/stream_video_link_background_dark</color>
    <color name="stream_video_overlay_regular">@color/stream_video_overlay_regular_dark</color>
    <color name="stream_video_overlay_dark">@color/stream_video_overlay_dark_dark</color>
    <color name="stream_video_primary_accent">@color/stream_video_primary_accent_dark</color>
    <color name="stream_video_error_accent">@color/stream_video_error_accent_dark</color>
    <color name="stream_video_info_accent">@color/stream_video_info_accent_dark</color>
    <color name="stream_video_highlight">@color/stream_video_highlight_dark</color>
</resources>
