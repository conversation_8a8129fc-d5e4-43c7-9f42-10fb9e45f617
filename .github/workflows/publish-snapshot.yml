name: Publish Snapshot builds

on:
  push:
    branches:
      - develop
  workflow_dispatch:

jobs:
  publish:
    name: <PERSON>nap<PERSON> build and publish
    runs-on: ubuntu-24.04
    steps:
      - name: Check out code
        uses: actions/checkout@v3.1.0

      - uses: GetStream/android-ci-actions/actions/setup-java@main

      - name: Release build
        # assembleRelease for all modules, excluding non-library modules: samples, docs
        run: ./gradlew assembleRelease -x :demo-app:assembleRelease

      - name: Source jar and dokka
        run: ./gradlew androidSourcesJar javadocJar

      - name: Publish to MavenCentral
        run: ./gradlew publishReleasePublicationToSonatypeRepository
        env:
          OSSRH_USERNAME: ${{ secrets.OSSRH_USERNAME }}
          OSSRH_PASSWORD: ${{ secrets.OSSRH_PASSWORD }}
          SIGNING_KEY_ID: ${{ secrets.SIGNING_KEY_ID }}
          SIGNING_PASSWORD: ${{ secrets.SIGNING_PASSWORD }}
          SIGNING_KEY: ${{ secrets.SIGNING_KEY }}
          SONATYPE_STAGING_PROFILE_ID: ${{ secrets.SONATYPE_STAGING_PROFILE_ID }}
          SNAPSHOT: true
      - name: Show snapshot version
        run: ./scripts/show-last-snapshot-update.sh
        env:
          SNAPSHOT: true
