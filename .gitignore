*.iml
.composite
buildSrc/build
.gradle
/local.properties
.env.properties

# Built application files
*.apk
*.aar
*.ap_
*.aab
*.exec

library/.env

# Files for the ART/Dalvik VM
*.dex

# Java class files
*.class

# Kotlin class files
.kotlin

# Generated files
bin/
gen/
out/
#  Uncomment the following line in case you need and you don't have the release build type files in your app
# release/

# Gradle files
/.idea
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Proguard folder generated by Eclipse
proguard/

# Log Files
*.log

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# Keystore files
# Uncomment the following lines if you do not want to check your keystore files in.
#*.jks
#*.keystore

# External native build folder generated in Android Studio 2.2 and later
.externalNativeBuild
.cxx/

# Google Services (e.g. APIs or Firebase)
# google-services.json

# Freeline
freeline.py
freeline/
freeline_project_description.json

# fastlane
!fastlane/.env
fastlane/fastlane.log
fastlane/report.xml
fastlane/screenshots
fastlane/test_output
fastlane/allurectl
fastlane/recordings
fastlane/test-parser.jar
fastlane/AllTests.txt
allure-results

# Version control
vcs.xml
.gitignore.swp

# lint
lint/intermediates/
lint/generated/
lint/outputs/
lint/tmp/
# lint/reports/

/.idea/*
!/.idea/codeInsightSettings.xml
!/.idea/codeStyles/
!/.idea/inspectionProfiles/
!/.idea/scopes/
.DS_Store
/build
/captures
/attachments/*
.cxx
/projectFilesBackup/
/projectFilesBackup1/
*.gpg

# Ignore Dokka files into docusaurus project
docusaurus/docs/Android/Dokka
# Ignore Algolia credentials
docusaurus/.env

# Ignore keysotore files
.sign/keystore.properties
.sign/release.keystore

# Ignore Google Play Publisher files
.sign/service-account-credentials.json
demo-app/src/production/play/

# Environment Variables
.env.properties

# Stream Video Buddy
video-buddy-server.log
video-buddy-console.log
video-buddy-session.json
