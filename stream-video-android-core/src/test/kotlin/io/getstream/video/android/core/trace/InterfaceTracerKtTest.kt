/*
 * Copyright (c) 2014-2024 Stream.io Inc. All rights reserved.
 *
 * Licensed under the Stream License;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    https://github.com/GetStream/stream-video-android/blob/main/LICENSE
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.getstream.video.android.core.trace

import io.mockk.spyk
import io.mockk.verify
import org.junit.Test

interface TestInterface {
    fun foo(x: Int): String
    fun bar(y: String): Int
}

class TestImpl : TestInterface {
    override fun foo(x: Int) = "foo$x"
    override fun bar(y: String) = y.length
}

class InterfaceTracerKtTest {
    @Test
    fun `tracedWith proxies method calls and traces them`() {
        val tracer = spyk(Tracer("iface"))
        val impl = TestImpl()
        val proxy = tracedWith<TestInterface>(impl, tracer)

        val fooResult = proxy.foo(42)
        val barResult = proxy.bar("hello")

        verify { tracer.trace("foo", match { it is Array<*> && it[0] == 42 }) }
        verify { tracer.trace("bar", match { it is Array<*> && it[0] == "hello" }) }
        assert(fooResult == "foo42")
        assert(barResult == 5)
    }
}
