<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (c) 2024 Stream.io Inc. All rights reserved.

     Licensed under the Stream License;
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          https://github.com/GetStream/stream-video-android/blob/main/LICENSE

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>
    <declare-styleable name="ControlButton">
        <!-- Sets the button background. -->
        <attr name="android:background" format="reference" />
        <!-- Sets the button background. -->
        <attr name="android:backgroundTint" format="color|reference" />
        <!-- Sets the button background. -->
        <attr name="android:src" format="reference" />
        <!-- Sets the button background. -->
        <attr name="android:tint" format="color|reference" />
        <!-- Sets the button background. -->
        <attr name="android:enabled" format="reference" />
        <!-- Sets the button background. -->
        <attr name="streamVideoControlButtonBackgroundEnabledAlpha" format="float|reference" />
        <!-- Sets the button background. -->
        <attr name="streamVideoControlButtonBackgroundDisabledAlpha" format="float|reference" />
    </declare-styleable>
</resources>