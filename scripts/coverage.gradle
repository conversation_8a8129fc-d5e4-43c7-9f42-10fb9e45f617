if (!rootProject.ext.sonar.ignoreModules.contains(name)) {
    apply plugin: "org.jetbrains.kotlinx.kover"
    apply plugin: "org.sonarqube"

    if (hasProperty('android')) {
        android {
            buildTypes {
                debug {
                    testCoverageEnabled = true
                    enableUnitTestCoverage = true
                    enableAndroidTestCoverage true
                }
            }
        }
    }

    kover {
        reports {
            verify {
                warningInsteadOfFailure = true
            }
        }
        currentProject {
            instrumentation {
                excludedClasses.addAll(
                        "io.getstream.android.video.generated.*",
                        "io.getstream.video.android.core.model.*"
                )
            }
        }
    }

    sonarqube {
        properties {
            property "sonar.junit.reportPaths", "${buildDir}/test-results/testDebugUnitTest"
            property "sonar.coverage.jacoco.xmlReportPaths", "${buildDir}/reports/kover/reportDebug.xml"
            property "sonar.sources", "src/main/kotlin"
            property "sonar.coverage.exclusions", "**/generated/**,**/io/getstream/video/android/core/model/**"
        }
    }
}
