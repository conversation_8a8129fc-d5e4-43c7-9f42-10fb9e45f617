import io.getstream.video.android.Configuration

// Create variables with empty default values
ext["ossrhUsername"] = ''
ext["ossrhPassword"] = ''
ext["sonatypeStagingProfileId"] = ''
ext["signing.keyId"] = ''
ext["signing.password"] = ''
ext["signing.key"] = ''
ext["snapshot"] = ''

File secretPropsFile = project.rootProject.file('local.properties')
if (secretPropsFile.exists()) {
  // Read local.properties file first if it exists
  Properties p = new Properties()
  new FileInputStream(secretPropsFile).withCloseable { is -> p.load(is) }
  p.each { name, value -> ext[name] = value }
} else {
  // Use system environment variables
  ext["ossrhUsername"] = System.getenv('OSSRH_USERNAME')
  ext["ossrhPassword"] = System.getenv('OSSRH_PASSWORD')
  ext["sonatypeStagingProfileId"] = System.getenv('SONATYPE_STAGING_PROFILE_ID')
  ext["signing.keyId"] = System.getenv('SIGNING_KEY_ID')
  ext["signing.password"] = System.getenv('SIGNING_PASSWORD')
  ext["signing.key"] = System.getenv('SIGNING_KEY')
  ext["snapshot"] = System.getenv('SNAPSHOT')
}

if (snapshot) {
  ext["rootVersionName"] = Configuration.snapshotVersionName
} else {
  ext["rootVersionName"] = Configuration.versionName
}

// Set up Sonatype repository
nexusPublishing {
  repositories {
    sonatype {
      stagingProfileId = sonatypeStagingProfileId
      username = ossrhUsername
      password = ossrhPassword
      version = rootVersionName
    }
  }
}

tasks.register("printAllArtifacts") {
    group = "publishing"

    doLast {
        subprojects.each { subproject ->
            subproject.plugins.withId("maven-publish") {
                def publishingExtension = subproject.extensions.findByType(PublishingExtension)
                publishingExtension?.publications?.all { publication ->
                    if (publication instanceof MavenPublication) {
                        def groupId = publication.groupId
                        def artifactId = publication.artifactId
                        def version = publication.version
                        println("$groupId:$artifactId:$version")
                    }
                }
            }
        }
    }
}